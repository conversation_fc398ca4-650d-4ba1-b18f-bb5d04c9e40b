// 使用示例文件
const cryptoModule = require('./extracted_crypto_function.js');

console.log("=== 极速赛车向前冲 - 提取的加密函数使用示例 ===\n");

// 获取主要对象和函数
const { xN, targetFunction, secondFunction, it, tn, qt } = cryptoModule;

console.log("1. 变量信息:");
console.log("   it (加密方法名):", it);
console.log("   tn (加密方法名):", tn);
console.log("   qt (解密方法名):", qt);
console.log();

console.log("2. 核心函数测试:");

// 测试你要找的核心函数
console.log("   测试核心函数 xN[it][tn]:");
try {
    // 这是你要找的函数：xN[it][tn] = function(r, t) { ... }
    const result1 = xN[it][tn]("hello", "world");
    console.log("   输入: r='hello', t='world'");
    console.log("   输出:", result1);
} catch (e) {
    console.error("   错误:", e.message);
}

console.log();

console.log("   测试第二个函数 xN[it][qt]:");
try {
    const result2 = xN[it][qt]("hello", "world");
    console.log("   输入: r='hello', t='world'");
    console.log("   输出:", result2);
} catch (e) {
    console.error("   错误:", e.message);
}

console.log();

console.log("3. 直接调用函数:");
try {
    const directResult = targetFunction("test", "message");
    console.log("   直接调用结果:", directResult);
} catch (e) {
    console.error("   错误:", e.message);
}

console.log();

console.log("4. 函数签名分析:");
console.log("   原始函数签名: xN[it][tn] = function(r, t) { ... }");
console.log("   - 参数 r: 字符串参数，如果不为空会添加 '1' 后缀");
console.log("   - 参数 t: 数据参数，会被 Base64 编码");
console.log("   - 返回值: 经过处理的字符串");

console.log();
console.log("5. 函数逻辑:");
console.log("   1. 检查 r 是否不等于空字符串，如果不等于则添加 '1'");
console.log("   2. 对参数 t 进行 Base64 编码");
console.log("   3. 调用 _DesCreate 方法处理结果");
console.log("   4. 返回最终结果");

// 演示如何在你的代码中使用
console.log();
console.log("6. 在你的代码中使用:");
console.log(`
// 引入模块
const crypto = require('./extracted_crypto_function.js');

// 方法1: 使用解构赋值
const { xN, it, tn } = crypto;
const result = xN[it][tn]("prefix", "data");

// 方法2: 直接使用导出的函数
const result2 = crypto.targetFunction("prefix", "data");

// 方法3: 使用全局对象（如果设置了的话）
// const result3 = global.M.encrypt.encrypt("prefix", "data");
`);
