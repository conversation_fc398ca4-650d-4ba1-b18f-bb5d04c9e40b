// 从极速赛车向前冲游戏中提取的加密函数
// 可以在 Node.js 环境中独立运行

var r, e, u, o, s, a, f, h, c, v, l, d, w, b, g, p, E, y, k, S, m, A, C, I, x, R, D, B, P, _, T, H,
    K, N, M, O, U, F, L, V, j, G, Z, $, Q, z, Y, J, X, W, q, rr, tr, nr, ir, er, ur, or, sr, ar, fr,
    hr, cr, vr, lr, dr, wr, br, gr, pr, Er, yr, kr, Sr, mr, Ar, Cr, Ir, xr, Rr, Dr, Br, Pr, _r, Tr,
    Hr, Kr, Nr, Mr, Or, Ur, Fr, Lr, Vr, jr, Gr, Zr, $r, Qr, zr, Yr, Jr, Xr, Wr, qr, rt, tt, nt, it,
    et, ut, ot, st, at, ft, ht, ct, vt, lt, dt, wt, bt, gt, pt, Et, yt, kt, St, mt, At, Ct, It, xt,
    Rt, Dt, Bt, Pt, _t, Tt, Ht, Kt, Nt, Mt, Ot, Ut, Ft, Lt, Vt, jt, Gt, Zt, $t, Qt, zt, Yt, Jt, Xt,
    Wt, qt, rn, tn, nn, en, un, on, sn, an, fn, hn, cn, vn, ln, dn, wn, bn, gn, pn, En, yn, kn, Sn,
    mn, An, Cn, In, xn, Rn, Dn, Bn, Pn, _n, Tn, Hn, Kn, Nn, Mn, On, Un, Fn, Ln, Vn, jn, Gn, Zn, $n,
    Qn, zn, Yn, Jn, Xn, Wn, qn, ri, ti, ni, ii, ei, ui, oi, si, ai, fi, hi, ci, vi, li, di, wi, bi,
    gi, pi, Ei, yi, ki, Si, mi, Ai, Ci, Ii, xi, Ri, Di, Bi, Pi, _i, Ti, Hi, Ki, Ni, Mi, Oi, Ui, Fi,
    Li, Vi, ji, Gi, Zi, $i, Qi, zi, Yi, Ji, Xi, Wi, qi, re, te, ne, ie, ee, ue, oe, se, ae, fe, he,
    ce, ve, le, de, we, be, ge, pe, Ee, ye, ke, Se, me, Ae, Ce, Ie, xe, Re, De, Be, Pe, _e, Te, He,
    Ke, Ne, Me, Oe, Ue, Fe, Le, Ve, je, Ge, Ze, $e, Qe, ze, Ye, Je, Xe, We, qe, ru, tu, nu, iu, eu,
    uu, ou, su, au, fu, hu, cu, vu, lu, du, wu, bu, gu, pu, Eu, yu, ku, Su, mu, Au, Cu, Iu, xu, Ru,
    Du, Bu, Pu, _u, Tu, Hu, Ku, Nu, Mu, Ou, Uu, Fu, Lu, Vu, ju, Gu, Zu, $u, Qu, zu, Yu, Ju, Xu, Wu,
    qu, ro, to, no, io, eo, uo, oo, so, ao, fo, ho, co, vo, lo, wo, bo, go, po, Eo, yo, ko, So, mo,
    Ao, Co, Io, xo, Ro, Do, Bo, Po, _o, To, Ho, Ko, No, Mo, Oo, Uo, Fo, Lo, Vo, jo, Go, Zo, $o, Qo,
    zo, Yo, Jo, Xo, Wo, qo, rs, ts, ns, is, es, us, os, ss, as, fs, hs, cs, vs, ls, ds, ws, bs, gs,
    ps, Es, ys, ks, Ss, ms, As, Cs, Is, xs, Rs, Ds, Bs, Ps, _s, Ts, Hs, Ks, Ns, Ms, Os, Us, Fs, Ls,
    Vs, js, Gs, Zs, $s, Qs, zs, Ys, Js, Xs, Ws, qs, ra, ta, na, ia, ea, ua, oa, sa, aa, fa, ha, ca,
    va, la, da, wa, ba, ga, pa, Ea, ya, ka, Sa, ma, Aa, Ca, Ia, xa, Ra, Da, Ba, Pa, _a, Ta, Ha, Ka,
    Na, Ma, Oa, Ua, Fa, La, Va, ja, Ga, Za, $a, Qa, za, Ya, Ja, Xa, Wa, qa, rf, tf, nf, ef, uf, of,
    sf, af, ff, hf, cf, vf, lf, df, wf, bf, gf, pf, Ef, yf, kf, Sf, mf, Af, Cf, If, xf, Rf, Df, Bf,
    Pf, _f, Tf, Hf, Kf, Nf, Mf, Of, Uf, Ff, Lf, Vf, jf, Gf, Zf, $f, Qf, zf, Yf, Jf, Xf, Wf, qf, rh,
    th, nh, ih, eh, uh, oh, sh, ah, fh, hh, ch, vh, lh, dh, wh, bh, gh, ph, Eh, yh, kh, Sh, mh, Ah,
    Ch, Ih, xh, Rh, Dh, Bh, Ph, _h, Th, Hh, Kh, Nh, Mh, Oh, Uh, Fh, Lh, Vh, jh, Gh, Zh, $h, Qh, zh,
    Yh, Jh, Xh, Wh, qh, rc, tc, nc, ic, ec, uc, oc, sc, ac, fc, hc, cc, vc, lc, dc, wc, bc, gc, pc,
    Ec, yc, kc, Sc, mc, Ac, Cc, Ic, xc, Rc, Dc, Bc, Pc, _c, Tc, Hc, Kc, Nc, Mc, Oc, Uc, Fc, Lc, Vc,
    jc, Gc, Zc, $c, Qc, zc, Yc, Jc, Xc, Wc, qc, rv, tv, nv, iv, ev, uv, ov, sv, av, fv, hv, cv, vv,
    lv, dv, wv, bv, gv, pv, Ev, yv, kv, Sv, mv, Av, Cv, Iv, xv, Rv, Dv, Bv, Pv, _v, Tv, Hv, Kv, Nv,
    Mv, Ov, Uv, Fv, Lv, Vv, jv, Gv, Zv, $v, Qv, zv, Yv, Jv, Xv, Wv, qv, rl, tl, nl, il, el, ul, ol,
    sl, al, fl, hl, cl, vl, ll, dl, wl, bl, gl, pl, El, yl, kl, Sl, ml, Al, Cl, Il, xl, Rl, Dl, Bl,
    Pl, _l, Tl, Hl, Kl, Nl, Ml, Ol, Ul, Fl, Ll, Vl, jl, Gl, Zl, $l, Ql, zl, Yl, Jl, Xl, Wl, ql, rd,
    td, nd, id, ed, ud, od, sd, ad, fd, hd, cd, vd, ld, dd, wd, bd, gd, pd, Ed, yd, kd, Sd, md, Ad,
    Cd, Id, xd, Rd, Dd, Bd, Pd, _d, Td, Hd, Kd, Nd, Md, Od, Ud, Fd, Ld, Vd, jd, Gd, Zd, $d, Qd, zd,
    Yd, Jd, Xd, Wd, qd, rw, tw, nw, iw, ew, uw, ow, sw, aw, fw, hw, cw, vw, lw, dw, ww, bw, gw, pw,
    Ew, yw, kw, Sw, mw, Aw, Cw, Iw, xw, Rw, Dw, Bw, Pw, _w, Tw, Hw, Kw, Nw, Mw, Ow, Uw, Fw, Lw, Vw,
    jw, Gw, Zw, $w, Qw, zw, Yw, Jw, Xw, Ww, qw, rb, tb, nb, ib, eb, ub, ob, sb, ab, fb, hb, cb, vb,
    lb, db, wb, bb, gb, pb, Eb, yb, kb, Sb, mb, Ab, Cb, Ib, xb, Rb, Db, Bb, Pb, _b, Tb, Hb, Kb, Nb,
    Mb, Ob, Ub, Fb, Lb, Vb, jb, Gb, Zb, $b, Qb, zb, Yb, Jb, Xb, Wb, qb, rg, tg, ng, ig, eg, ug, og,
    sg, ag, fg, hg, cg, vg, lg, dg, wg, bg, gg, pg, Eg, yg, kg, Sg, mg, Ag, Cg, Ig, xg, Rg, Dg, Bg,
    Pg, _g, Tg, Hg, Kg, Ng, Mg, Og, Ug, Fg, Lg, Vg, jg, Gg, Zg, $g, Qg, zg, Yg, Jg, Xg, Wg, qg, rp,
    tp, np, ip, ep, up, op, sp, ap, fp, hp, cp, vp, lp, dp, wp, bp, gp, pp, Ep, yp, kp, Sp, mp, Ap,
    Cp, Ip, xp, Rp, Dp, Bp, Pp, _p, Tp, Hp, Kp, Np, Mp, Op, Up, Fp, Lp, Vp, jp, Gp, Zp, $p, Qp, zp,
    Yp, Jp, Xp, Wp, qp, rE, tE, nE, iE, eE, uE, oE, sE, aE, fE, hE, cE, vE, lE, dE, wE, bE, gE, pE,
    EE, yE, kE, SE, mE, AE, CE, IE, xE, RE, DE, BE, PE, _E, TE, HE, KE, NE, ME, OE, UE, FE, LE, VE,
    jE, GE, ZE, $E, QE, zE, YE, JE, XE, WE, qE, ry, ty, ny, iy, ey, uy, oy, sy, ay, fy, hy, cy, vy,
    ly, dy, wy, by, gy, py, Ey, yy, ky, Sy, my, Ay, Cy, Iy, xy, Ry, Dy, By, Py, _y, Ty, Hy, Ky, Ny,
    My, Oy, Uy, Fy, Ly, Vy, jy, Gy, Zy, $y, Qy, zy, Yy, Jy, Xy, Wy, qy, rk, tk, nk, ik, ek, uk, ok,
    sk, ak, fk, hk, ck, vk, lk, dk, wk, bk, gk, pk, Ek, yk, kk, Sk, mk, Ak, Ck, Ik, xk, Rk, Dk, Bk,
    Pk, _k, Tk, Hk, Kk, Nk, Mk, Ok, Uk, Fk, Lk, Vk, jk, Gk, Zk, $k, Qk, zk, Yk, Jk, Xk, Wk, qk, rS,
    tS, nS, iS, eS, uS, oS, sS, aS, fS, hS, cS, vS, lS, dS, wS, bS, gS, pS, ES, yS, kS, SS, mS, AS,
    CS, IS, xS, RS, DS, BS, PS, _S, TS, HS, KS, NS, MS, OS, US, FS, LS, VS, jS, GS, ZS, $S, QS, zS,
    YS, JS, XS, WS, qS, rm, tm, nm, im, em, um, om, sm, am, fm, hm, cm, vm, lm, dm, wm, bm, gm, pm,
    Em, ym, km, Sm, mm, Am, Cm, Im, xm, Rm, Dm, Bm, Pm, _m, Tm, Hm, Km, Nm, Mm, Om, Um, Fm, Lm, Vm,
    jm, Gm, Zm, $m, Qm, zm, Ym, Jm, Xm, Wm, qm, rA, tA, nA, iA, eA, uA, oA, sA, aA, fA, hA, cA, vA,
    lA, dA, wA, bA, gA, pA, EA, yA, kA, SA, mA, AA, CA, IA, xA, RA, DA, BA, PA, _A, TA, HA, KA, NA,
    MA, OA, UA, FA, LA, VA, jA, GA, ZA, $A, QA, zA, YA, JA, XA, WA, qA, rC, tC, nC, iC, eC, uC, oC,
    sC, aC, fC, hC, cC, vC, lC, dC, wC, bC, gC, pC, EC, yC, kC, SC, mC, AC, CC, IC, xC, RC, DC, BC,
    PC, _C, TC, HC, KC, NC, MC, OC, UC, FC, LC, VC, jC, GC, ZC, $C, QC, zC, YC, JC, XC, WC, qC, rI,
    tI, nI, iI, eI, uI, oI, sI, aI, fI, hI, cI, vI, lI, dI, wI, bI, gI, pI, EI, yI, kI, SI, mI, AI,
    CI, II, xI, RI, DI, BI, PI, _I, TI, HI, KI, NI, MI, OI, UI, FI, LI, VI, jI, GI, ZI, $I, QI, zI,
    YI, JI, XI, WI, qI, rx, tx, nx, ix, ex, ux, ox, sx, ax, fx, hx, cx, vx, lx, dx, wx, bx, gx, px,
    Ex, yx, kx, Sx, mx, Ax, Cx, Ix, xx, Rx, Dx, Bx, Px, _x, Tx, Hx, Kx, Nx, Mx, Ox, Ux, Fx, Lx, Vx,
    jx, Gx, Zx, $x, Qx, zx, Yx, Jx, Xx, Wx, qx, rR, tR, nR, iR, eR, uR, oR, sR, aR, fR, hR, cR, vR,
    lR, dR, wR, bR, gR, pR, ER, yR, kR, SR, mR, AR, CR, IR, xR, RR, DR, BR, PR, _R, TR, HR, KR, NR,
    MR, OR, UR, FR, LR, VR, jR, GR, ZR, $R, QR, zR, YR, JR, XR, WR, qR, rD, tD, nD, iD, eD, uD, oD,
    sD, aD, fD, hD, cD, vD, lD, dD, wD, bD, gD, pD, ED, yD, kD, SD, mD, AD, CD, ID, xD, RD, DD, BD,
    PD, _D, TD, HD, KD, ND, MD, OD, UD, FD, LD, VD, jD, GD, ZD, $D, QD, zD, YD, JD, XD, WD, qD, rB,
    tB, nB, iB, eB, uB, oB, sB, aB, fB, hB, cB, vB, lB, dB, wB, bB, gB, pB, EB, yB, kB, SB, mB, AB,
    CB, IB, xB, RB, DB, BB, PB, _B, TB, HB, KB, NB, MB, OB, UB, FB, LB, VB, jB, GB, ZB, $B, QB, zB,
    YB, JB, XB, WB, qB, rP, tP, nP, iP, eP, uP, oP, sP, aP, fP, hP, cP, vP, lP, dP, wP, bP, gP, pP,
    EP, yP, kP, SP, mP, AP, CP, IP, xP, RP, DP, BP, PP, _P, TP, HP, KP, NP, MP, OP, UP, FP, LP, VP,
    jP, GP, ZP, $P, QP, zP, YP, JP, XP, WP, qP, r_, t_, n_, i_, e_, u_, o_, s_, a_, f_, h_, c_, v_,
    l_, d_, w_, b_, g_, p_, E_, y_, k_, S_, m_, A_, C_, I_, x_, R_, D_, B_, P_, __, T_, H_, K_, N_,
    M_, O_, U_, F_, L_, V_, j_, G_, Z_, $_, Q_, z_, Y_, J_, X_, W_, q_, rT, tT, nT, iT, eT, uT, oT,
    sT, aT, fT, hT, cT, vT, lT, dT, wT, bT, gT, pT, ET, yT, kT, ST, mT, AT, CT, IT, xT, RT, DT, BT,
    PT, _T, TT, HT, KT, NT, MT, OT, UT, FT, LT, VT, jT, GT, ZT, $T, QT, zT, YT, JT, XT, WT, qT, rH,
    tH, nH, iH, eH, uH, oH, sH, aH, fH, hH, cH, vH, lH, dH, wH, bH, gH, pH, EH, yH, kH, SH, mH, AH,
    CH, IH, xH, RH, DH, BH, PH, _H, TH, HH, KH, NH, MH, OH, UH, FH, LH, VH, jH, GH, ZH, $H, QH, zH,
    YH, JH, XH, WH, qH, rK, tK, nK, iK, eK, uK, oK, sK, aK, fK = Math.log,
    hK = (Math.pow, Math.floor),
    cK = (Math.random, Math.exp),
    vK = Math.abs,
    lK = Math.round,
    dK = (Math.E, Math.LN10, Math.LN2, Math.LOG10E, Math.PI, Math.SQRT1_2, Math.SQRT2, 77),
    wK = 91,
    bK = 72,
    gK = 70,
    pK = 76,
    EK = (hK(214 / 3), hK(cK((fK(47) + fK(75) + fK(92)) / 3)), lK(2 * fK(vK(8651))) <= lK(fK(8117) +
        fK(9221))),
    yK = hK(77) >= hK(cK((fK(83) + fK(54) + fK(76) + fK(95)) / 4)),
    kK = (lK(2 * fK(vK(9937))), lK(fK(7585) + fK(14593)), lK(2 * fK(vK(9238))) > lK(fK(11197) + fK(
        7625))),
    SK = lK(2 * fK(vK(12201))) <= lK(fK(17885) + fK(9881)),
    mK = (lK(2 * fK(vK(6166))), lK(fK(9850) + fK(10874)), hK(158 / 3), hK(cK((fK(dK) + fK(45) + fK(
        36)) / 3)), hK(37.75), hK(cK((fK(75) + fK(14) + fK(21) + fK(41)) / 4)), lK(2 * fK(vK(
        10060))), lK(fK(15034) + fK(6994)), hK(58.75) < hK(cK((fK(bK) + fK(75) + fK(37) + fK(
        51)) / 4))),
    AK = hK(54.25) < hK(cK((fK(51) + fK(92) + fK(4) + fK(70)) / 4)),
    CK = (lK(2 * fK(vK(2401))), lK(fK(2225) + fK(10874)), hK(62.75) >= hK(cK((fK(pK) + fK(96) + fK(
        39) + fK(40)) / 4))),
    IK = (hK(233 / 3), hK(cK((fK(51) + fK(90) + fK(92)) / 3)), lK(2 * fK(vK(5996))) <= lK(fK(3908) +
        fK(10804))),
    xK = hK(65.25) >= hK(cK((fK(19) + fK(83) + fK(63) + fK(96)) / 4)),
    RK = lK(2 * fK(vK(9653))) <= lK(fK(13573) + fK(7333)),
    DK = hK(172 / 3) < hK(cK((fK(96) + fK(4) + fK(bK)) / 3)),
    BK = hK(193 / 3) >= hK(cK((fK(gK) + fK(27) + fK(96)) / 3)),
    PK = hK(52.5) >= hK(cK((fK(37) + fK(47) + fK(36) + fK(90)) / 4)),
    _K = (lK(2 * fK(vK(5558))), lK(fK(2938) + fK(10778)), lK(2 * fK(vK(11362))) > lK(fK(10370) + fK(
        13378))),
    TK = (hK(47), hK(cK((fK(51) + fK(15) + fK(75)) / 3)), lK(2 * fK(vK(8750))), lK(fK(7250) + fK(
        12250)), hK(22) >= hK(cK((fK(8) + fK(21) + fK(37)) / 3))),
    HK = (hK(74.25), hK(cK((fK(37) + fK(gK) + fK(95) + fK(95)) / 4)), 1),
    KK = 1,
    NK = 1,
    MK = 1,
    OK = 1,
    UK = 1,
    FK = 1,
    LK = 1,
    VK = 1,
    jK = 1,
    GK = 1,
    ZK = 1,
    $K = 1,
    QK = 1,
    zK = 1,
    YK = 1,
    JK = 1,
    XK = 1,
    WK = 1,
    qK = 1,
    rN = 1,
    tN = 1,
    nN = 1,
    iN = 1,
    eN = 1,
    uN = 1,
    oN = 1,
    sN = 1,
    aN = 1,
    fN = 1,
    hN = 1,
    cN = 1,
    vN = 1,
    lN = 1,
    dN = 1,
    wN = 1,
    bN = 1,
    gN = 1,
    pN = 1,
    EN = 1,
    yN = 1;
// 字符串解码函数 kN
var kN = function(r, t) {
    for (var n = r.length, i = [], e = 0; e < n; ++e) {
        var u = -1;
        2 === t && (u = 5 * (e - 46) % 132) < 0 && (u += n), 3 === t && (u = 137 * (e - 213) %
                222) < 0 && (u += n), 4 === t && (u = 235 * (e - 63) % 324) < 0 && (u += n),
            5 === t && (u = 183 * (e - 54) % 310) < 0 && (u += n), 6 === t && (u = 335 * (e -
                65) % 624) < 0 && (u += n), 7 === t && (u = 446 * (e - 68) % 455) < 0 && (u +=
                n), 8 === t && (u = 361 * (e - 57) % 680) < 0 && (u += n), 9 === t && (u = 668 *
                (e - 36) % 747) < 0 && (u += n), 10 === t && (u = 391 * (e - 11) % 820) < 0 && (
                u += n), 11 === t && (u = 251 * (e - 0) % 616) < 0 && (u += n), 12 === t && (u =
                79 * (e - 4) % 456) < 0 && (u += n), 13 === t && (u = 126 * (e - 13) % 403) <
            0 && (u += n), 14 === t && (u = 263 * (e - 81) % 350) < 0 && (u += n), 15 === t && (
                u = 328 * (e - 61) % 465) < 0 && (u += n), 16 === t && (u = 369 * (e - 96) %
                416) < 0 && (u += n), 17 === t && (u = 165 * (e - 45) % 272) < 0 && (u += n),
            18 === t && (u = 391 * (e - 46) % 450) < 0 && (u += n), 19 === t && (u = 78 * (e -
                19) % 323) < 0 && (u += n), 20 === t && (u = 219 * (e - 1) % 260) < 0 && (u +=
                n), 21 === t && (u = 187 * (e - 45) % 210) < 0 && (u += n), 22 === t && (u =
                47 * (e - 13) % 264) < 0 && (u += n), 23 === t && (u = 203 * (e - 55) % 253) <
            0 && (u += n), 24 === t && (u = 289 * (e - 90) % 312) < 0 && (u += n), 25 === t && (
                u = 169 * (e - 0) % 225) < 0 && (u += n), 26 === t && (u = 33 * (e - 21) % 52) <
            0 && (u += n), 27 === t && (u = 8 * (e - 42) % 351) < 0 && (u += n), 28 === t && (
                u = 89 * (e - 11) % 168) < 0 && (u += n), 29 === t && (u = 188 * (e - 6) %
            203) < 0 && (u += n), 30 === t && (u = 257 * (e - 219) % 270) < 0 && (u += n),
            31 === t && (u = 95 * (e - 26) % 124) < 0 && (u += n), 32 === t && (u = 17 * (e -
                39) % 64) < 0 && (u += n), 33 === t && (u = 146 * (e - 20) % 165) < 0 && (u +=
                n), 34 === t && (u = 1 * (e - 134) % 34) < 0 && (u += n), 35 === t && (u = 6 * (
                e - 45) % 35) < 0 && (u += n), 36 === t && (u = 95 * (e - 87) % 504) < 0 && (
                u += n), 37 === t && (u = 124 * (e - 10) % 185) < 0 && (u += n), 38 === t && (
                u = 191 * (e - 118) % 304) < 0 && (u += n), 39 === t && (u = 41 * (e - 11) %
                156) < 0 && (u += n), 40 === t && (u = 107 * (e - 90) % 120) < 0 && (u += n),
            41 === t && (u = 25 * (e - 1) % 41) < 0 && (u += n), 42 === t && (u = 101 * (e -
                323) % 168) < 0 && (u += n), 43 === t && (u = 83 * (e - 49) % 129) < 0 && (u +=
                n), 44 === t && (u = 69 * (e - 163) % 220) < 0 && (u += n), 46 === t && (u =
                51 * (e - 73) % 92) < 0 && (u += n), 47 === t && (u = 13 * (e - 56) % 94) < 0 &&
            (u += n), 48 === t && (u = 325 * (e - 26) % 336) < 0 && (u += n), 49 === t && (u =
                29 * (e - 15) % 49) < 0 && (u += n), 50 === t && (u = 177 * (e - 56) % 250) <
            0 && (u += n), u >= 0 && (i[u] = r[e])
    }
    return i.join("")
};

// 字符串原型扩展
String.prototype.m = String.prototype.substr;
String.prototype.s = function(r) {
    for (var t = [], n = 0; n < this.length; n += r) t.push(this.slice(n, n + r));
    return t
};
var BN = kN(
    "s-taelln6smyIgraepsPcaxat1fuvVtyefrepc9peraszs4aONMnendi5geahiccmfHj_otisoo6 e _tnpa6xr_ytsaih1.mrednAo1h-amcSgg4eapndSlHeeEhscsBHytkaomttrerpaehv_eetttbEugDgyc2eps_nthpyepUrtcucukiteleeehrsvaHsetves(Rixo_rusa-leohNtucittih_eacMakuhyEbfelroSLauo2-maKtTiroujnleEant1rr_bebuaPxuNieTbkt1sBneuwme1hKleft_axetanePdrte2ElpniahtpnoggioxvpDi_r_tXi8yuDdteypt2fmseyosiurbctBSs__r[vsidinrSSbKtrrsSfsIycnnOdavaeixoPdthcodgxrtalc5Nyusfmif1dreefuWa31ntvsemttpeePrftdo4]ahypnAnteyriisnls1^kttdtkeSiKNhciti8enDahdb1de5ygarueeeptintiryop6D_bttes89eftsttoi86-reisdLoarcoitoatrRx_HeeSgfptypth1tes0ersawSTbgeOmhprz_ctapaej6cr1Onlmbsbx2axorvero2H tKaTsPt2rrStTdrl40-_NzdAebserwndtty1I_mehxNi8replsiArte-yeptiio6nyWdaliedpethsfe0oi2bZgalclt5lstaaeik5eprenotrok.otHoyd_r",
    9).s(9);
// 解码后的字符串数组
var RN = kN("penetsnyexiibptnfifstpoOeAsm0eeistssEDEmbmp_lufItPoxItcudpeiskeNetadpSneepEbeciescuinrCiFZstpKnRetletetfncysSnlznlt_crnaieBmxaOie0nuftosiSgE_lehBdtlnern_n_ofit_theeacnceeSDtfuRoxmd0ttnxSovmI_cdp_aNxeafanVrthmptceed_airegugsip5nAgh0tttBROo-eSe_deLoeafdovatlmmniksHygmreexndeDlbRoRdn0ristSceeXTsyNclueitlldiooe_aronhetlnypilnitu0ZEnm0ifuiapnCtZndirOFDtlevepgemagoeleSzeyxeOdesaicNlS5a0gmheLerDERynaiZlcneagcdmpsbdodbaxrgdplntPggt9_PHaom8ttdepBT_desaCiEeafDr", 7).s(7);

var HN = kN("rhaegwteihemAMy6eDlttiEdt _dReB4aSSAhtnDhmc1As6EdAtSShcERir6Ms4nZprNHRoRSse0Earciai1ASdUAmaSTgsoprnO2AeTSatHEeadSsgb2SdFHteARDEetedj4HH8AcH2Sin_rPaewAeS1he2ggcBiKtci5xtwjl4eeranCatt1nrispwtsysgS ih2eitoeiJtpeS5tnDwwnhnrtWpt6APoiSiFgETDhKui4LEotAtagCoERFboDTM  ShleDARSrkne_dlfHRstSSEAoehcLeoaASeHANnSmymoEcni2AAaS1uHKAadNrgl5drsHHmAelce_y_e6orhAEe3ygrEHpBdwFaL2Xr8HNiCLta.iiye5ga4aap EKs:tnsn6etwsmePNee", 13).s(13);

var PN = kN("e0bssbtlleecoraeBueSD-E8l0iab5tPiceaDMnm6pyara4fHa4chpI sIrirsbKOat0yneMatoCoaaTt_eRsFe00nnsmucOadtn1gxtUSt^g2w2AFBso icrhIdx_HSgesiblnahtgsD_riHOuat:Hm08hayhuedkhoenrlta2f-R1a4v5l6dUne lNodeHbttat0semhiieLehNstfhk-__i0TggDsTdrdtmHaptB5Ln02g2eamsbrecdpefSTHr]e3ibdiyhnKthdaTIpwAatnet8tStaehs__etLbmle Za01SlLRsnAeadadsoenHr-I0t0a1ehiBd_etadshmkei_e0exfmpfrTriGearodIFbo0ope5tLieucsrprMoPAg02dbtcehjlAhfstoeCAi*ren8dnPaaEsZasSnla5lhZxe1itD2xtiDevPEjburp_x00ttiShhSyr_mmicsDmo-n4e0t2smcLomrLmeiiadLcbR2tsoahtcErf HsorfnLanahstw_VfalhsspeioBTt02e8PsraeetnisOrcT2n$sxf1Oaa3rYs_thTvit1tD_tr0pra2tgses_aNeeeDkD_04rlsAemNcCm gokgEam-t0R1edeatIwareemsgcfe_ia1mirceRlRotiAnmt2fUss2asVifbitegtaVvnlEEhDbf1rha2cnwilSImoi5gbttl0flr8ysuFa_Rece2pENra4lit4leMsMmrGcrgEcEl00iethna1hoaefn_eRcS[e0afdotciCBtonBoEhS2nltw2ugmsriaErpnS1NVIoS", 10).s(10);

var xN = kN("gysdagkNmtesen5yesrsomqmYeeyaPgtnThat8en_oe5svtC1iatrmsfvuu0sdT0elez2aerrToDeascssriftKlhet2egeintInec aIsuDd1lthntdbuobrahrBuruEanr4sdbuc6tKiiEpihyisyepb1wxL1ge_e5rtetodatlVeteevSreaaqo4siandrndtoLtnubEs2eJauceeaduideaKpbmX2detcoitibrempvolopltlneK0_BV0anpe6APdedetBgEHr6astvyg3ubScndsistoOnautlsRtvnW5mhkrrPfvwxmDdmcT2NvoswtppiiyehpfSdtlrebre1mEf0tgaxStEulidaioRag4tariss8ajHdteevhpmIctnittSaeiK1suegeofS_SsFapoe4oeBposuh_ag_eKfHloiinimyfaGogetrtHkMcsgentiIszkelisiZ4leA_wrriarpDaigsmretrs$2tnye0weisHc2tspnotrilcBteb5exrDsA_bsndtorfsIrerhseAegetecusdFhheptnsg_ssc3diwtd2nr_tnzMectuiEsprkit1nrziAhgetycnsttitacrustfsFe2dih", 6).s(6);

var FN = kN(
    "_sltsE gi nCn ieeceDcerrc_yihBuiha mNtCsadannmorEr6dcPeizpapdMlReasEunolgtedgRy4B Knneasp etEmmiRfomgFiAeeOp_amCcfhreows QoenTfnHorflutbtusiSooes6rislUrsvIiieroignPjites8dPae4tnaeEyiaFccximegkEeofdmHeadP_edgnSiglIiaOtHrenMcn8Cae_seuudoegTnniCelfhegtoStS_itxBsrbt wHtuvWdAnANmxeRwtIcdpceaw lfk ahnai Ttlb:itanrdhehhnsoci8ess",
    19).s(19);

// 变量定义
var it = BN[0];  // 从 BN 数组中获取索引 0 的值
var tn = RN[3];  // 从 RN 数组中获取索引 3 的值  
var qt = RN[5];  // 从 RN 数组中获取索引 5 的值
var vP = HN[20]; // 从 HN 数组中获取索引 20 的值
var cP = FN[7];
var hP = "_Base64Encode"; // 根据代码分析得出的完整字符串
var dP = PN[42]; // 从 PN 数组中获取索引 42 的值
var iK = "";     // 空字符串
var Fg = "1";    // 字符串 "1"
var FA = "M";    // 字符串 "M"

// 创建 xN 构造函数对象
// 在第231行之前添加：
xN[it] = xN[it] || {}; // 确保 xN[it] 存在

// 然后才能设置：

xN[it][hP] = function(r) {
    var t, n, i, e, o, s, a, c, v;
    for (c = "", v = 0, r = this[cP](r); v < r[$];) o = (t = r[tr](
        v++)) >> l, s = (h & t) << u | (a = r[tr](v++)) >> u, e = (w &
        a) << l | (i = r[tr](v++)) >> f, n = Rr & i, isNaN(a) ? e =
        n = kt : isNaN(i) && (n = kt), c = c + this[cr][rr](o) + this[
        cr][rr](s) + this[cr][rr](e) + this[cr][rr](n);
    return c
}
xN[it][vP] = function(r) {
    var t, n, i, e, o, s, a, c;
    for (s = "", i = 0, r = r[G](/[^A-Za-z0-9+/=]/g, ""); i < r[$];) t =
        this[cr][jr](r[rr](i++)), n = (w & (o = this[cr][jr](r[rr](
        i++)))) << u | (e = this[cr][jr](r[rr](i++))) >> l, c = (h &
        e) << f | (a = this[cr][jr](r[rr](i++))), s += String[Q](
        t << l | o >> u), kt != e && (s += String[Q](n)), kt != a &&
    (s += String[Q](c));
    return this[lP](s)
}
xN[it][cP] = function(r) {
    r = r[G](/rn/g, Ut);
    for (var t = "", n = 0; n < r[$]; n++) {
        var i;
        (i = r[tr](n)) < Ct ? t += String[Q](i) : t = cv < i && i < mb ?
            (t += String[Q](i >> f | Et)) + String[Q](Rr & i | Ct) : (
            t = (t += String[Q](i >> E | It)) + String[Q](i >> f &
                Rr | Ct)) + String[Q](Rr & i | Ct)
    }
    return t
}
xN[it][lP] = function(r) {
    for (var t, n, i = "", e = 0, u = 0; e < r[$];)(n = r[tr](e)) < Ct ?
        (i += String[Q](n), e++) : bt < n && n < It ? (u = r[tr](e + 1),
            i += String[Q]((A & n) << f | Rr & u), e += l) : (u = r[tr](
            e + 1), t = r[tr](e + l), i += String[Q]((w & n) << E |
            (Rr & u) << f | Rr & t), e += h);
    return i
}
xN[it][dP] = function(t, n, i) {
    var o, s;
    if (o = n, qt === i) {
        for (var a = "", f = wP == n[rt](0, l) ? l : 0; f < n[$]; f +=
            l) a += String[Q](parseInt(n[rt](f, l), e));
        o = a
    }
    for (var c, v, b, g, p, E, y, k, S = new Array(bP, 0, DE, gP, pP,
            EP, u, DE, gD, bP, gP, gD, yP, pP, kP, u, SP, mP, mP,
            AP, AP, CP, CP, yP, IP, xP, xP, IP, 0, SP, EP, kP, DE,
            gP, u, CP, bP, kP, kP, gD, pP, DE, AP, xP, gD, u, yP,
            EP, gP, IP, CP, yP, xP, SP, EP, bP, SP, mP, mP, 0, IP,
            AP, 0, pP), m = new Array(-RP, -DP, WD, BP, PP, T, -_P,
            -TP, -HP, -RP, -KP, -NP, -DP, PP, T, -_P, MP, OP, -TP,
            0, -NP, WD, BP, -UP, OP, -HP, 0, MP, FP, -KP, -UP, FP,
            0, BP, -_P, PP, -TP, -UP, -KP, WD, -UP, -DP, T, -RP, BP,
            T, WD, -NP, FP, -KP, PP, -HP, OP, -TP, -HP, OP, MP, 0, -
                DP, FP, -NP, -_P, -RP, MP), x = new Array(LP, VP, 0, jP,
            GP, 0, ZP, GP, $P, QP, QP, zP, YP, $P, JP, LP, XP, r,
            VP, XI, WP, JP, jP, ZP, qP, WP, zP, qP, r, YP, XI, XP,
            VP, XP, $P, LP, zP, VP, GP, 0, XI, $P, YP, GP, QP, XI,
            0, jP, qP, zP, XP, YP, r, ZP, WP, QP, JP, qP, LP, JP,
            ZP, r, jP, WP), R = new Array(r_, t_, t_, Ct, n_, i_,
            e_, ZD, 0, u_, u_, o_, ND, 0, s_, e_, 1, a_, f_, r_, Ct,
            f_, ZD, h_, i_, 1, h_, s_, a_, n_, o_, ND, s_, e_, u_,
            o_, ND, 0, 0, u_, h_, s_, i_, 1, r_, t_, t_, Ct, o_, ND,
            1, a_, e_, ZD, n_, i_, ZD, h_, f_, r_, Ct, f_, a_, n_),
             D = new Array(Sr, c_, v_, l_, d_, Sr, w_, v_, b_, d_, g_,
                 b_, l_, p_, E_, w_, y_, k_, k_, 0, S_, m_, m_, g_, p_,
                 S_, 0, A_, c_, y_, A_, E_, d_, l_, Sr, y_, w_, v_, l_,
                 b_, g_, w_, p_, c_, b_, Sr, y_, p_, m_, E_, A_, m_, v_,
                 0, k_, A_, E_, g_, S_, d_, 0, k_, c_, S_), B =
            new Array(C_, I_, eI, x_, I_, e, x_, iB, R_, D_, iB, C_, B_,
                R_, P_, __, 0, B_, T_, eI, H_, T_, e, K_, K_, 0, D_, N_,
                __, H_, N_, P_, R_, e, K_, H_, x_, iB, __, C_, iB, R_,
                P_, __, C_, x_, H_, I_, D_, N_, 0, K_, e, eI, I_, D_,
                eI, B_, T_, 0, N_, P_, B_, T_), P = new Array(M_, O_,
            U_, 0, mb, U_, F_, L_, V_, M_, 0, j_, l, Fd, O_, G_, Z_,
            F_, $_, Z_, j_, Q_, L_, $_, Q_, mb, G_, V_, z_, l, Fd,
            z_, Fd, z_, M_, U_, U_, O_, O_, l, $_, Fd, Z_, M_, L_,
            G_, F_, L_, G_, j_, V_, Q_, z_, 0, l, V_, 0, F_, Q_, mb,
            j_, Z_, mb, $_), _ = new Array(Y_, lD, J_, X_, W_, Y_,
            kt, W_, q_, rT, X_, tT, nT, iT, lD, kt, rT, eT, uT, oT,
            tT, q_, sT, nT, oT, 0, 0, sT, eT, uT, iT, J_, iT, J_,
            nT, lD, kt, sT, lD, iT, uT, kt, eT, rT, sT, W_, J_, Y_,
            0, X_, q_, eT, rT, uT, Y_, 0, X_, tT, tT, oT, oT, q_,
            W_, nT), H = this[aT](t), K = 0, N = o[$], O = 0, U =
            T == H[$] ? h : d, F = h == U ? tn === i ? new Array(0, T,
            l) : new Array(I, -l, -l) : tn === i ? new Array(0, T,
            l, fT, I, -l, kt, nB, l) : new Array(hT, fT, -l, T, kt,
            l, I, -l, -l), L = "", V = ""; K < N;) {
        for (p = o[tr](K++) << M | o[tr](K++) << e | o[tr](K++) << r |
            o[tr](K++), p = (p = (p = (p = (p = (p ^= (v = cT & (p >>>
            u ^ (E = o[tr](K++) << M | o[tr]
                (K++) << e | o[tr](K++) <<
                r | o[tr](K++)))) << u) ^ (v = Ow &
            (p >>> e ^ (E ^= v))) << e) ^ (v = vT & ((
            E ^= v) >>> l ^ p))) ^ (v = lT & ((E ^= v <<
            l) >>> r ^ p))) ^ (v = dT & (p >>> 1 ^ (E ^= v <<
            r))) << 1) << 1 | p >>> A, E = (E ^= v) << 1 | E >>> A,
                 c = 0; c < U; c += h) {
            for (y = F[c + 1], k = F[c + l], f = F[c]; f != y; f += k)
                b = E ^ H[f], g = (E >>> u | E << C) ^ H[f + 1], v = p,
                    p = E, E = v ^ (m[b >>> M & Rr] | R[b >>> e & Rr] | B[
                b >>> r & Rr] | _[Rr & b] | S[g >>> M & Rr] | x[
                g >>> e & Rr] | D[g >>> r & Rr] | P[Rr & g]);
            v = p, p = E, E = v
        }
        E = E >>> 1 | E << A, E = (E = (E = (E = (E ^= v = dT & ((p =
            p >>> 1 | p << A) >>> 1 ^ E)) ^ (v = lT & (
            E >>> r ^ (p ^= v << 1))) << r) ^ (v = vT & (
            E >>> l ^ (p ^= v))) << l) ^ (v = Ow & ((p ^= v) >>>
            e ^ E))) ^ (v = cT & ((p ^= v << e) >>> u ^ E)), p ^=
            v << u, V += String[Q](p >>> M, p >>> e & q, p >>> r & q,
            q & p, E >>> M, E >>> e & q, E >>> r & q, q & E), XI ==
        (O += r) && (L += V, V = "", O = 0)
    }
    if (s = L + V, tn !== i) return L + V;
    var j = "",
        G = new Array(er, Fg, wT, bT, gT, RE, pT, ET, PE, yT, sb, kT,
            ST, Lt, Ft, $b);
    for (f = 0; f < s[$]; f++) j += G[s[tr](f) >> u] + G[w & s[tr](f)];
    return j
}
xN[it][aT] = function(t) {
    for (var n, i, a = new Array(0, u, P_, mT, DE, IP, AT, CT, XI, IT,
        xT, RT, DT, BT, PT, _T), f = new Array(0, 1, PP, TT, Fd,
        HT, KT, NT, Sr, Hv, MT, OT, UT, FT, LT, VT), c =
        new Array(0, r, mb, jT, kP, GT, ZT, $T, 0, r, mb, jT, kP,
            GT, ZT, $T), v = new Array(0, M_, XP, QT, a_, zT, YT,
        JT, zP, XT, JP, WT, qT, rH, tH, nH), d = new Array(0,
        J_, e, iH, 0, J_, e, iH, lD, tT, eH, uH, lD, tT, eH, uH
    ), b = new Array(0, gD, T, oH, 0, gD, T, oH, y_, sH, aH,
        fH, y_, sH, aH, fH), g = new Array(0, W_, d_, hH, l, cH,
        vH, lH, 0, W_, d_, hH, l, cH, vH, lH), p = new Array(0,
        DE, mb, dH, P_, AT, wH, bH, zP, gH, pH, EH, yH, kH, SH,
        mH), y = new Array(0, J_, 0, J_, l, AH, l, AH, y_, CH,
        y_, CH, IH, xH, IH, xH), k = new Array(0, W_, r, RH, 0,
        W_, r, RH, gD, DH, BH, PH, gD, DH, BH, PH), S =
        new Array(0, T, 0, T, PP, OP, PP, OP, a_, _H, a_, _H, TH,
            HH, TH, HH), m = new Array(0, kP, XI, KH, M_, NH, MH,
        OH, Fd, UH, FH, LH, Q_, VH, jH, GH), A = new Array(0,
        lD, XP, ZH, d_, $H, QH, zH, e, eH, YH, JH, XH, WH, qH,
        rK), I = new Array(0, u, Sr, tK, 0, u, Sr, tK, 1, o, Hv,
        nK, 1, o, Hv, nK), x = r < t[$] ? h : 1, R = new Array(
        T * x), D = new Array(0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1,
        1, 1, 1, 1, 0), B = 0, H = 0, K = 0; K < x; K++) {
        O = t[tr](B++) << M | t[tr](B++) << e | t[tr](B++) << r | t[tr](
            B++);
        for (var N = (O = (O = (O = (O = (O = (O = (O ^= (N = cT & (
                    O >>> u ^ (U = t[tr]
                        (B++) << M | t[
                            tr](B++) <<
                        e | t[tr](
                            B++) << r | t[
                            tr](B++)))) <<
                u) ^ (N = Ow & ((U ^= N) >>>
                -e ^ O))) ^ (N = vT & (O >>> l ^
                (U ^= N << -e))) << l) ^ (N = Ow & (
                (U ^= N) >>> -e ^ O))) ^ (N = dT & (
                O >>> 1 ^ (U ^= N << -e))) << 1) ^ (N = lT &
                ((U ^= N) >>> r ^ O))) ^ (N = dT & (O >>> 1 ^ (
                U ^= N << r))) << 1) << r | (U ^= N) >>> s & xt, O =
                U << M | U << r & X | U >>> r & W | U >>> M & xt, U = N,
                 F = 0; F < D[$]; F++) U = D[F] ? (O = O << l | O >>> _,
        U << l | U >>> _) : (O = O << 1 | O >>> P, U << 1 |
        U >>> P), U &= -w, n = a[(O &= -w) >>> C] | f[O >>> M &
            w] | c[O >>> s & w] | v[O >>> e & w] | d[O >>> E & w] |
            b[O >>> r & w] | g[O >>> u & w], i = p[U >>> C] | y[U >>>
        M & w] | k[U >>> s & w] | S[U >>> e & w] | m[U >>> E &
        w] | A[U >>> r & w] | I[U >>> u & w], R[H++] = n ^ (N =
            Ow & (i >>> e ^ n)), R[H++] = i ^ N << e
    }
    return R
}
xN[it][tn] = function(r, t) {
    var n;
    return iK != r && (r += Fg), n = this[hP](t), this[dP](r, n, tn)
}
xN[it][qt] = function(r, t) {
    var n;
    return iK != r && (r += Fg), n = this[dP](r, t, qt), this[vP](n)
}

xN[it][tn]('111','1111');